import React, { useState, useCallback, useMemo } from 'react'
import { useNavigate } from 'react-router-dom'
import { EnhancedModernTable, EnhancedColumnDef, CRUDOperations } from './ui/enhanced-modern-table'
import { ComplaintData } from '../../../shared/api'
import { FiEye, FiFileText, FiBarChart3 } from 'react-icons/fi'
import { cn } from '../lib/aceternity-utils'
import { useThemeContext } from '../context/useThemeContext'

// Transform ComplaintData to table row format
interface ComplaintTableRow {
  id: string
  complaint_number: string
  complainant_name: string
  category_of_fraud: string
  date_of_complaint: string
  amount: number
  status: string
  file_name?: string
  fraud_type?: string
  created_at: string
  updated_at: string
}

interface EnhancedComplaintTableProps {
  complaints: ComplaintData[]
  loading?: boolean
  onRefresh?: () => void
}

const EnhancedComplaintTable: React.FC<EnhancedComplaintTableProps> = ({
  complaints,
  loading = false,
  onRefresh
}) => {
  const navigate = useNavigate()
  const { isDark } = useThemeContext()

  // Transform complaints data for table
  const tableData = useMemo((): ComplaintTableRow[] => {
    return complaints.map((complaint) => ({
      id: complaint.id,
      complaint_number: (complaint.metadata?.complaint_number as string) || complaint.id.slice(0, 8),
      complainant_name: (complaint.metadata?.complainant_name as string) || 'Unknown',
      category_of_fraud: (complaint.metadata?.subcategory as string) || complaint.fraud_type || 'Unknown',
      date_of_complaint: (complaint.metadata?.date as string) || complaint.created_at,
      amount: parseFloat(String(complaint.metadata?.total_amount || 0).replace(/,/g, '')),
      status: complaint.status || 'processed',
      file_name: complaint.file_name,
      fraud_type: complaint.fraud_type,
      created_at: complaint.created_at,
      updated_at: complaint.updated_at
    }))
  }, [complaints])

  // Define table columns
  const columns: EnhancedColumnDef<ComplaintTableRow>[] = useMemo(() => [
    {
      key: 'complaint_number',
      title: 'Complaint Number',
      type: 'text',
      editable: true,
      required: true,
      width: 150
    },
    {
      key: 'complainant_name',
      title: 'Complainant Name',
      type: 'text',
      editable: true,
      required: true,
      width: 200
    },
    {
      key: 'category_of_fraud',
      title: 'Category of Fraud',
      type: 'select',
      editable: true,
      options: [
        { value: 'banking_upi', label: 'Banking UPI' },
        { value: 'credit_card', label: 'Credit Card' },
        { value: 'debit_card', label: 'Debit Card' },
        { value: 'net_banking', label: 'Net Banking' },
        { value: 'mobile_banking', label: 'Mobile Banking' },
        { value: 'other', label: 'Other' }
      ],
      width: 180
    },
    {
      key: 'amount',
      title: 'Amount',
      type: 'number',
      editable: true,
      render: (value: number) => (
        <span className={cn(
          'font-medium',
          value > 100000 ? 'text-red-600 dark:text-red-400' : 'text-green-600 dark:text-green-400'
        )}>
          ₹{value.toLocaleString('en-IN')}
        </span>
      ),
      width: 120
    },
    {
      key: 'date_of_complaint',
      title: 'Date',
      type: 'date',
      editable: true,
      render: (value: string) => new Date(value).toLocaleDateString('en-IN'),
      width: 120
    },
    {
      key: 'status',
      title: 'Status',
      type: 'select',
      editable: true,
      options: [
        { value: 'processed', label: 'Processed' },
        { value: 'pending', label: 'Pending' },
        { value: 'completed', label: 'Completed' },
        { value: 'rejected', label: 'Rejected' }
      ],
      render: (value: string) => (
        <span className={cn(
          'px-2 py-1 rounded-full text-xs font-medium',
          {
            'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200': value === 'completed',
            'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200': value === 'processed',
            'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200': value === 'pending',
            'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200': value === 'rejected'
          }
        )}>
          {value.charAt(0).toUpperCase() + value.slice(1)}
        </span>
      ),
      width: 100
    },
    {
      key: 'actions',
      title: 'Actions',
      type: 'custom',
      render: (_, row: ComplaintTableRow) => (
        <div className="flex items-center gap-1">
          <button
            onClick={(e) => {
              e.stopPropagation()
              navigate(`/complaint-details/${row.id}`)
            }}
            className={cn(
              'p-2 rounded-lg transition-colors',
              'hover:bg-blue-100 dark:hover:bg-blue-900',
              'text-blue-600 dark:text-blue-400'
            )}
            title="View Details"
          >
            <FiEye size={16} />
          </button>
          <button
            onClick={(e) => {
              e.stopPropagation()
              navigate(`/graph-visualization/${row.id}`)
            }}
            className={cn(
              'p-2 rounded-lg transition-colors',
              'hover:bg-green-100 dark:hover:bg-green-900',
              'text-green-600 dark:text-green-400'
            )}
            title="Graph Visualization"
          >
            <FiBarChart3 size={16} />
          </button>
          <button
            onClick={(e) => {
              e.stopPropagation()
              navigate(`/notice-generation/${row.id}`)
            }}
            className={cn(
              'p-2 rounded-lg transition-colors',
              'hover:bg-purple-100 dark:hover:bg-purple-900',
              'text-purple-600 dark:text-purple-400'
            )}
            title="Generate Notice"
          >
            <FiFileText size={16} />
          </button>
        </div>
      ),
      width: 120
    }
  ], [navigate, isDark])

  // CRUD operations
  const operations: CRUDOperations<ComplaintTableRow> = {
    onCreate: async (data) => {
      try {
        // Create new complaint in database
        const newComplaint: Omit<ComplaintData, 'id' | 'created_at' | 'updated_at'> = {
          title: `Complaint - ${data.complaint_number}`,
          description: `Fraud complaint for ${data.complainant_name}`,
          file_name: '',
          fraud_type: data.category_of_fraud,
          extraction_type: 'manual',
          max_layer: 7,
          metadata: {
            complaint_number: data.complaint_number,
            complainant_name: data.complainant_name,
            subcategory: data.category_of_fraud,
            total_amount: data.amount,
            date: data.date_of_complaint
          },
          transactions: {},
          layer_transactions: {},
          bank_notice_data: {},
          graph_data: { nodes: [], edges: [], transactions: [], metadata: {}, max_layer: 7 },
          extraction_info: {},
          status: data.status
        }

        const id = await window.api.database.storeComplaint(newComplaint)
        onRefresh?.()
        
        return {
          ...data,
          id,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }
      } catch (error) {
        console.error('Failed to create complaint:', error)
        throw error
      }
    },

    onUpdate: async (id, data) => {
      try {
        // Get current complaint
        const currentComplaint = complaints.find(c => c.id === id)
        if (!currentComplaint) throw new Error('Complaint not found')

        // Update metadata
        const updatedMetadata = {
          ...currentComplaint.metadata,
          complaint_number: data.complaint_number,
          complainant_name: data.complainant_name,
          subcategory: data.category_of_fraud,
          total_amount: data.amount,
          date: data.date_of_complaint
        }

        const updateData = {
          metadata: updatedMetadata,
          status: data.status,
          fraud_type: data.category_of_fraud
        }

        await window.api.database.updateComplaint(id, updateData)
        onRefresh?.()

        return {
          ...tableData.find(row => row.id === id)!,
          ...data,
          updated_at: new Date().toISOString()
        }
      } catch (error) {
        console.error('Failed to update complaint:', error)
        throw error
      }
    },

    onDelete: async (id) => {
      try {
        await window.api.database.deleteComplaint(id)
        onRefresh?.()
      } catch (error) {
        console.error('Failed to delete complaint:', error)
        throw error
      }
    }
  }

  // Handle row click to navigate to details
  const handleRowClick = useCallback((row: ComplaintTableRow) => {
    navigate(`/complaint-details/${row.id}`)
  }, [navigate])

  return (
    <EnhancedModernTable
      data={tableData}
      columns={columns}
      operations={operations}
      loading={loading}
      onRowClick={handleRowClick}
      config={{
        enableSearch: true,
        enablePagination: true,
        enableSorting: true,
        enableSelection: false,
        enableGlassmorphism: true,
        enableRowActions: false, // We have custom actions column
        pageSize: 15,
        searchPlaceholder: 'Search complaints...'
      }}
      emptyMessage="No complaints found. Upload a complaint file to get started."
      className="w-full"
    />
  )
}

export default EnhancedComplaintTable
